<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\BalanceController;
use App\Http\Controllers\CurrencyController;
use App\Http\Controllers\MessageController;
use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Broadcast;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Test auth route
Route::middleware('auth:sanctum')->get('/test-auth', function (Request $request) {
    $user = $request->user();
    return response()->json([
        'authenticated' => !!$user,
        'user_id' => $user ? $user->id : null,
        'username' => $user ? $user->username : null
    ]);
});

Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);
Route::post('/logout', [AuthController::class, 'logout'])->middleware('auth:sanctum');

// Message routes
Route::get('/messages/history', [MessageController::class, 'history']); // Public route for chat history


Route::middleware('auth:sanctum')->group(function () {
    Route::post('/messages', [MessageController::class, 'send']);
});

// Profile routes
Route::middleware('auth:sanctum')->group(function () {
    Route::put('/profile', [ProfileController::class, 'updateProfile']);
    Route::put('/profile/password', [ProfileController::class, 'updatePassword']);
    Route::post('/profile/image', [ProfileController::class, 'updateProfileImage']);
    Route::delete('/profile/image', [ProfileController::class, 'deleteProfileImage']);
});

// Currency routes (public)
Route::get('/currencies', [CurrencyController::class, 'index']);
Route::get('/currencies/{currency}', [CurrencyController::class, 'show']);

// Balance and transaction routes (authenticated)
Route::middleware('auth:sanctum')->group(function () {
    // Balance operations
    Route::get('/balances', [BalanceController::class, 'index']);
    Route::get('/balances/{currency}', [BalanceController::class, 'show']);
    Route::post('/transactions', [BalanceController::class, 'processTransaction']);
    Route::get('/transactions/{currency}', [BalanceController::class, 'transactions']);
});
