# Multi-Currency Balance and Transaction System

This system implements a secure, atomic, and verifiable multi-currency balance and transaction system with hash verification.

## Features

- **Multi-currency support**: USD, USDT, and POINTS
- **Atomic transactions**: All balance changes are atomic and consistent
- **Hash verification**: Every transaction and balance has cryptographic hash verification
- **Append-only transactions**: Transaction history is immutable
- **Chain verification**: Transactions form a verifiable hash chain
- **Soft deletes**: Users can be soft-deleted while preserving transaction history

## Database Schema

### Tables Created

1. **currencies**
   - `id`: Primary key
   - `code`: Enum(USD, USDT, POINTS) - unique
   - `decimals`: Number of decimal places
   - `symbol`: Currency symbol

2. **users** (modified)
   - Added `deleted_at` for soft deletes

3. **user_balances**
   - `id`: Primary key
   - `user_id`: Foreign key to users
   - `currency_id`: Foreign key to currencies
   - `balance`: BIGINT (in minor units)
   - `balance_hash`: SHA-256 hash for verification
   - `updated_at`: Last update timestamp
   - Unique constraint on (user_id, currency_id)

4. **transactions**
   - `id`: Primary key
   - `user_id`: Foreign key to users
   - `currency_id`: Foreign key to currencies
   - `amount`: BIGINT (signed, positive or negative)
   - `balance_before`, `balance_after`: BIGINT
   - `type`: Transaction type string
   - `reference_id`: Optional reference
   - `meta`: Optional JSON metadata
   - `previous_hash`: Hash of previous transaction
   - `hash`: SHA-256 hash of this transaction
   - `created_at`: Creation timestamp
   - No deletes allowed (append-only)

## API Endpoints

### Public Endpoints

- `GET /api/currencies` - List all currencies
- `GET /api/currencies/{currency}` - Get specific currency details

### Authenticated Endpoints (require Bearer token)

- `GET /api/balances` - Get all user balances
- `GET /api/balances/{currency}` - Get balance for specific currency
- `POST /api/transactions` - Process a transaction
- `GET /api/transactions/{currency}` - Get transaction history

## Enhanced BalanceService Methods

The system provides specific methods for balance operations:

### Core Methods

1. **`credit(user, currency, amount, type, reference_id?, meta?)`**
   - Adds funds to a user's balance
   - Amount must be positive (in minor units)
   - Creates a positive transaction

2. **`debit(user, currency, amount, type, reference_id?, meta?)`**
   - Subtracts funds from a user's balance
   - Amount must be positive (in minor units)
   - Creates a negative transaction
   - Validates sufficient balance

3. **`getBalance(user, currency)`**
   - Returns numeric balance (integer in minor units)
   - Returns 0 if no balance exists

4. **`getBalances(user)`**
   - Returns array of all balances grouped by currency code
   - Format: `['USD' => 10000, 'POINTS' => 500]`

5. **`startBalanceChain(user)`**
   - Creates user_balances entries with balance = 0
   - Creates genesis transactions with amount = 0
   - Initializes balance chains for all currencies

### Usage Examples

#### 1. Initialize User Balance Chains
```php
$balanceService = new BalanceService();
$results = $balanceService->startBalanceChain($user);
// Creates balance records for USD, USDT, POINTS with genesis transactions
```

#### 2. Credit User Balance
```php
$transaction = $balanceService->credit(
    $user,
    $usdCurrency,
    10000, // $100.00 in cents
    'deposit',
    'bank-transfer-123',
    ['source' => 'bank_account', 'account' => '****1234']
);
```

#### 3. Debit User Balance
```php
$transaction = $balanceService->debit(
    $user,
    $usdCurrency,
    5000, // $50.00 in cents
    'withdrawal',
    'withdrawal-456',
    ['destination' => 'bank_account']
);
```

#### 4. Check Balance
```php
$balance = $balanceService->getBalance($user, $usdCurrency);
// Returns: 5000 (in cents)
```

#### 5. Get All Balances
```php
$balances = $balanceService->getBalances($user);
// Returns: ['USD' => 5000, 'USDT' => 0, 'POINTS' => 0]
```

## API Usage Examples

### 1. Get Available Currencies
```bash
curl -X GET http://localhost/api/currencies
```

### 2. Process a Deposit Transaction
```bash
curl -X POST http://localhost/api/transactions \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "currency_id": 1,
    "amount": 100.50,
    "type": "deposit",
    "reference_id": "deposit-123",
    "meta": {"source": "bank_transfer"}
  }'
```

### 3. Process a Withdrawal Transaction
```bash
curl -X POST http://localhost/api/transactions \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "currency_id": 1,
    "amount": -50.25,
    "type": "withdrawal",
    "reference_id": "withdrawal-456"
  }'
```

### 4. Get User Balances
```bash
curl -X GET http://localhost/api/balances \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 5. Get Transaction History
```bash
curl -X GET http://localhost/api/transactions/1?limit=10&offset=0 \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## Hash Verification

### Transaction Hash
Each transaction's hash is calculated from (in this exact order):
```
user_id, currency_id, amount, balance_before, balance_after, type, reference_id, meta, created_at, previous_hash
```
- All fields are sorted alphabetically before hashing
- Meta is JSON-encoded if present, null otherwise
- Created_at is formatted as 'Y-m-d H:i:s'
- Hash algorithm: SHA-256

### Balance Hash
Each balance's hash is calculated from (in this exact order):
```
user_id, currency_id, balance, last_txn_id, last_txn_hash
```
- All fields are sorted alphabetically before hashing
- Hash algorithm: SHA-256

### Chain Verification
- **Genesis transaction**: `previous_hash = SHA256("genesis_{user_id}_{currency_id}")`
- **Subsequent transactions**: `previous_hash = previous_transaction.hash`
- **Chain integrity**: Each transaction's previous_hash must match the previous transaction's hash
- **Balance verification**: Balance hash must match the latest transaction ID and hash

### Concurrency Safety
- Uses `SELECT ... FOR UPDATE` on user_balances table
- All balance changes are wrapped in database transactions
- Atomic operations ensure consistency during concurrent access

## Security Features

1. **Atomic Operations**: All balance changes use database transactions
2. **Hash Verification**: Every transaction and balance can be cryptographically verified
3. **Chain Integrity**: Transaction chains prevent tampering
4. **Append-Only**: Transactions cannot be deleted or modified
5. **Locking**: Database row locking prevents race conditions
6. **Validation**: Comprehensive input validation and business rules

## Testing

Run the comprehensive test suite:
```bash
npm run test:backend
```

The test suite includes:
- Unit tests for models and services
- Integration tests for the balance service
- API endpoint tests
- Hash verification tests
- Transaction chain integrity tests
- Concurrent transaction tests

## Models and Services

### Key Classes

- `Currency`: Manages currency definitions and conversions
- `UserBalance`: Manages user balance records with hash verification
- `Transaction`: Manages transaction records with chain verification
- `BalanceService`: Core service for processing transactions atomically

### Service Methods

#### Core Transaction Methods
- `credit()`: Add funds to user balance (positive amount)
- `debit()`: Subtract funds from user balance (positive amount, validates sufficient balance)
- `getBalance()`: Get numeric balance for a specific currency
- `getBalances()`: Get all balances grouped by currency code
- `startBalanceChain()`: Initialize balance chains for all currencies

#### Verification Methods
- `verifyTransactionIntegrity()`: Verify transaction hash and chain integrity
- `verifyUserTransactionChain()`: Verify entire transaction chain for a user and currency

#### Internal Methods
- `generateTransactionHash()`: Generate SHA-256 hash for transactions
- `generateBalanceHash()`: Generate SHA-256 hash for balances
- `createGenesisTransaction()`: Create initial genesis transaction with amount = 0

## Error Handling

The system handles various error conditions:
- Insufficient balance (prevents negative balances unless explicitly allowed)
- Invalid currency or user
- Hash verification failures
- Database constraint violations
- Concurrent access conflicts

All errors are logged and appropriate HTTP status codes are returned via the API.

## Transaction Notes System

The system includes a comprehensive transaction notes feature that allows attaching human-readable notes to transactions without affecting hash integrity.

### Features

- **Multiple notes per transaction**: Each transaction can have unlimited notes
- **Hash integrity preserved**: Notes do not affect transaction hash calculations
- **Visibility control**: Notes can be internal or user-visible
- **Tagging system**: Notes can be tagged for easy filtering and organization
- **Soft deletes**: Notes can be deleted but are preserved for audit trails
- **Author tracking**: Notes track who created them
- **Bulk operations**: Support for adding notes to multiple transactions

### Database Schema

#### transaction_notes Table
- `id`: Primary key
- `transaction_id`: Foreign key to transactions (required)
- `author_id`: Optional foreign key to users (nullable)
- `note`: TEXT field for the note content
- `visibility`: ENUM('internal', 'user-visible') - default 'internal'
- `tag`: Optional string for filtering (e.g., 'manual_adjustment', 'review')
- `created_at`: Timestamp
- `deleted_at`: For soft deletes

### TransactionNoteService Methods

#### Core Methods
- `addNote(transaction, note, author?, visibility?, tag?)`: Add a note to a transaction
- `getNotesForTransaction(transaction, visibility?, tag?)`: Get all notes for a transaction with optional filtering
- `getUserVisibleNotes(transaction)`: Get only user-visible notes
- `getInternalNotes(transaction)`: Get only internal notes
- `updateNote(note, newNote, visibility?, tag?)`: Update an existing note
- `deleteNote(note)`: Soft delete a note

#### Utility Methods
- `getNotesByTag(tag, user?)`: Get notes by tag across all transactions
- `verifyHashIntegrity(transaction)`: Verify that notes don't affect transaction hash
- `getNoteStatistics(transaction)`: Get statistics about notes for a transaction
- `bulkAddNotes(transactionIds, note, author?, visibility?, tag?)`: Add same note to multiple transactions

### API Endpoints

#### Transaction Notes (Authenticated)
- `GET /api/transactions/{transaction}/notes` - Get all notes for a transaction
- `POST /api/transactions/{transaction}/notes` - Add a note to a transaction
- `GET /api/transactions/{transaction}/notes/{note}` - Get a specific note
- `PUT /api/transactions/{transaction}/notes/{note}` - Update a note
- `DELETE /api/transactions/{transaction}/notes/{note}` - Delete a note
- `GET /api/notes/by-tag?tag={tag}` - Get notes by tag

### Usage Examples

#### 1. Add a Note to a Transaction
```bash
curl -X POST http://localhost/api/transactions/123/notes \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "note": "Manual adjustment due to bank error",
    "visibility": "internal",
    "tag": "manual_adjustment"
  }'
```

#### 2. Get All Notes for a Transaction
```bash
curl -X GET http://localhost/api/transactions/123/notes \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 3. Filter Notes by Visibility
```bash
curl -X GET "http://localhost/api/transactions/123/notes?visibility=user-visible" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 4. Filter Notes by Tag
```bash
curl -X GET "http://localhost/api/transactions/123/notes?tag=review" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 5. Update a Note
```bash
curl -X PUT http://localhost/api/transactions/123/notes/456 \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "note": "Updated note content",
    "visibility": "user-visible",
    "tag": "updated"
  }'
```

#### 6. Get Notes by Tag Across All Transactions
```bash
curl -X GET "http://localhost/api/notes/by-tag?tag=review" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Hash Integrity Guarantee

The transaction notes system is designed to ensure that adding, updating, or deleting notes **never affects the transaction hash**. This is verified through:

1. **Separate table**: Notes are stored in a separate `transaction_notes` table
2. **No hash recalculation**: Transaction hashes are never recalculated when notes change
3. **Automated verification**: The `verifyHashIntegrity()` method can verify this guarantee
4. **Comprehensive testing**: Tests ensure notes don't affect hash integrity

### Note Visibility and Security

- **Internal notes**: Only visible to administrators and internal systems
- **User-visible notes**: Can be shown to the transaction owner
- **Author tracking**: All notes track who created them for accountability
- **Soft deletes**: Deleted notes are preserved for audit trails

### Common Use Cases

1. **Manual adjustments**: Document why manual balance adjustments were made
2. **Customer service**: Add notes about customer inquiries or issues
3. **Compliance**: Document regulatory or compliance-related information
4. **Reviews**: Mark transactions for review or investigation
5. **System integration**: Add notes from external systems or processes
