# Multi-Currency Balance and Transaction System

This system implements a secure, atomic, and verifiable multi-currency balance and transaction system with hash verification.

## Features

- **Multi-currency support**: USD, USDT, and POINTS
- **Atomic transactions**: All balance changes are atomic and consistent
- **Hash verification**: Every transaction and balance has cryptographic hash verification
- **Append-only transactions**: Transaction history is immutable
- **Chain verification**: Transactions form a verifiable hash chain
- **Soft deletes**: Users can be soft-deleted while preserving transaction history

## Database Schema

### Tables Created

1. **currencies**
   - `id`: Primary key
   - `code`: Enum(USD, USDT, POINTS) - unique
   - `decimals`: Number of decimal places
   - `symbol`: Currency symbol

2. **users** (modified)
   - Added `deleted_at` for soft deletes

3. **user_balances**
   - `id`: Primary key
   - `user_id`: Foreign key to users
   - `currency_id`: Foreign key to currencies
   - `balance`: BIGINT (in minor units)
   - `balance_hash`: SHA-256 hash for verification
   - `updated_at`: Last update timestamp
   - Unique constraint on (user_id, currency_id)

4. **transactions**
   - `id`: Primary key
   - `user_id`: Foreign key to users
   - `currency_id`: Foreign key to currencies
   - `amount`: BIGINT (signed, positive or negative)
   - `balance_before`, `balance_after`: BIGINT
   - `type`: Transaction type string
   - `reference_id`: Optional reference
   - `meta`: Optional JSON metadata
   - `previous_hash`: Hash of previous transaction
   - `hash`: SHA-256 hash of this transaction
   - `created_at`: Creation timestamp
   - No deletes allowed (append-only)

## API Endpoints

### Public Endpoints

- `GET /api/currencies` - List all currencies
- `GET /api/currencies/{currency}` - Get specific currency details

### Authenticated Endpoints (require Bearer token)

- `GET /api/balances` - Get all user balances
- `GET /api/balances/{currency}` - Get balance for specific currency
- `POST /api/transactions` - Process a transaction
- `GET /api/transactions/{currency}` - Get transaction history

## Usage Examples

### 1. Get Available Currencies
```bash
curl -X GET http://localhost/api/currencies
```

### 2. Process a Deposit Transaction
```bash
curl -X POST http://localhost/api/transactions \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "currency_id": 1,
    "amount": 100.50,
    "type": "deposit",
    "reference_id": "deposit-123",
    "meta": {"source": "bank_transfer"}
  }'
```

### 3. Process a Withdrawal Transaction
```bash
curl -X POST http://localhost/api/transactions \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "currency_id": 1,
    "amount": -50.25,
    "type": "withdrawal",
    "reference_id": "withdrawal-456"
  }'
```

### 4. Get User Balances
```bash
curl -X GET http://localhost/api/balances \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 5. Get Transaction History
```bash
curl -X GET http://localhost/api/transactions/1?limit=10&offset=0 \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## Hash Verification

### Transaction Hash
Each transaction's hash is calculated from:
- Transaction ID
- User ID
- Currency ID
- Amount
- Balance before/after
- Type
- Reference ID
- Metadata
- Previous transaction hash
- Created timestamp

### Balance Hash
Each balance's hash is calculated from:
- User ID
- Currency ID
- Current balance
- Last transaction ID
- Last transaction hash

### Chain Verification
- First transaction: `previous_hash = SHA256("genesis_{user_id}_{currency_id}")`
- Subsequent transactions: `previous_hash = previous_transaction.hash`

## Security Features

1. **Atomic Operations**: All balance changes use database transactions
2. **Hash Verification**: Every transaction and balance can be cryptographically verified
3. **Chain Integrity**: Transaction chains prevent tampering
4. **Append-Only**: Transactions cannot be deleted or modified
5. **Locking**: Database row locking prevents race conditions
6. **Validation**: Comprehensive input validation and business rules

## Testing

Run the comprehensive test suite:
```bash
npm run test:backend
```

The test suite includes:
- Unit tests for models and services
- Integration tests for the balance service
- API endpoint tests
- Hash verification tests
- Transaction chain integrity tests
- Concurrent transaction tests

## Models and Services

### Key Classes

- `Currency`: Manages currency definitions and conversions
- `UserBalance`: Manages user balance records with hash verification
- `Transaction`: Manages transaction records with chain verification
- `BalanceService`: Core service for processing transactions atomically

### Service Methods

- `processTransaction()`: Process a balance change atomically
- `getBalance()`: Get user balance for a currency
- `getAllBalances()`: Get all balances for a user
- `verifyTransactionIntegrity()`: Verify transaction and chain integrity
- `verifyUserTransactionChain()`: Verify entire transaction chain for a user

## Error Handling

The system handles various error conditions:
- Insufficient balance (prevents negative balances unless explicitly allowed)
- Invalid currency or user
- Hash verification failures
- Database constraint violations
- Concurrent access conflicts

All errors are logged and appropriate HTTP status codes are returned via the API.
