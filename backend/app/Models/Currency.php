<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Currency extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'code',
        'decimals',
        'symbol',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'decimals' => 'integer',
    ];

    /**
     * Available currency codes.
     */
    public const CODES = ['USD', 'USDT', 'POINTS'];

    /**
     * Get the user balances for this currency.
     */
    public function userBalances(): HasMany
    {
        return $this->hasMany(UserBalance::class);
    }

    /**
     * Get the transactions for this currency.
     */
    public function transactions(): HasMany
    {
        return $this->hasMany(Transaction::class);
    }

    /**
     * Convert amount from minor units to major units.
     */
    public function fromMinorUnits(int $amount): float
    {
        return $amount / pow(10, $this->decimals);
    }

    /**
     * Convert amount from major units to minor units.
     */
    public function toMinorUnits(float $amount): int
    {
        return (int) round($amount * pow(10, $this->decimals));
    }
}
