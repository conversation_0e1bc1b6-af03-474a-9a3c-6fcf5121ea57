<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Transaction extends Model
{
    /**
     * Indicates if the model should be timestamped.
     */
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'currency_id',
        'amount',
        'balance_before',
        'balance_after',
        'type',
        'reference_id',
        'meta',
        'previous_hash',
        'hash',
        'created_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'amount' => 'integer',
        'balance_before' => 'integer',
        'balance_after' => 'integer',
        'meta' => 'array',
        'created_at' => 'datetime',
    ];

    /**
     * Get the user that owns the transaction.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the currency for this transaction.
     */
    public function currency(): BelongsTo
    {
        return $this->belongsTo(Currency::class);
    }



    /**
     * Get the previous transaction in the chain for this user and currency.
     */
    public function getPreviousTransaction(): ?Transaction
    {
        return static::where('user_id', $this->user_id)
            ->where('currency_id', $this->currency_id)
            ->where('id', '<', $this->id)
            ->orderBy('id', 'desc')
            ->first();
    }

    /**
     * Verify the hash chain integrity.
     */
    public function verifyChain(): bool
    {
        $previousTransaction = $this->getPreviousTransaction();

        if ($previousTransaction) {
            return hash_equals($previousTransaction->hash, $this->previous_hash);
        }

        // First transaction should have a specific previous hash
        return $this->previous_hash === hash('sha256', "genesis_{$this->user_id}_{$this->currency_id}");
    }
}
