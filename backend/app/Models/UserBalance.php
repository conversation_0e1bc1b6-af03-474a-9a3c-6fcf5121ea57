<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserBalance extends Model
{
    /**
     * Indicates if the model should be timestamped.
     */
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'currency_id',
        'balance',
        'balance_hash',
        'updated_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'balance' => 'integer',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the user that owns the balance.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the currency for this balance.
     */
    public function currency(): BelongsTo
    {
        return $this->belongsTo(Currency::class);
    }

    /**
     * Generate hash for the balance.
     */
    public function generateHash(?int $lastTransactionId = null, ?string $lastTransactionHash = null): string
    {
        $data = [
            'user_id' => $this->user_id,
            'currency_id' => $this->currency_id,
            'balance' => $this->balance,
            'last_txn_id' => $lastTransactionId,
            'last_txn_hash' => $lastTransactionHash,
        ];

        ksort($data);
        return hash('sha256', json_encode($data));
    }

    /**
     * Verify the balance hash.
     */
    public function verifyHash(?int $lastTransactionId = null, ?string $lastTransactionHash = null): bool
    {
        $expectedHash = $this->generateHash($lastTransactionId, $lastTransactionHash);
        return hash_equals($expectedHash, $this->balance_hash);
    }

    /**
     * Update the balance hash.
     */
    public function updateHash(?int $lastTransactionId = null, ?string $lastTransactionHash = null): void
    {
        $this->balance_hash = $this->generateHash($lastTransactionId, $lastTransactionHash);
    }
}
