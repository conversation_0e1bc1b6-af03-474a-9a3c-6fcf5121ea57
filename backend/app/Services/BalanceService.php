<?php

namespace App\Services;

use App\Models\Currency;
use App\Models\Transaction;
use App\Models\User;
use App\Models\UserBalance;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class BalanceService
{
    /**
     * Process a balance change transaction atomically.
     */
    public function processTransaction(
        User $user,
        Currency $currency,
        int $amount,
        string $type,
        ?string $referenceId = null,
        ?array $meta = null
    ): Transaction {
        return DB::transaction(function () use ($user, $currency, $amount, $type, $referenceId, $meta) {
            // Lock the user balance for update to prevent race conditions
            $balance = UserBalance::where('user_id', $user->id)
                ->where('currency_id', $currency->id)
                ->lockForUpdate()
                ->first();

            // Create balance record if it doesn't exist
            if (!$balance) {
                $balance = $this->createInitialBalance($user, $currency);
            }

            $balanceBefore = $balance->balance;
            $balanceAfter = $balanceBefore + $amount;

            // Validate balance doesn't go negative (unless explicitly allowed)
            if ($balanceAfter < 0 && !$this->isNegativeBalanceAllowed($type)) {
                throw new \InvalidArgumentException('Insufficient balance');
            }

            // Get the previous transaction hash for chain verification
            $previousTransaction = Transaction::where('user_id', $user->id)
                ->where('currency_id', $currency->id)
                ->orderBy('id', 'desc')
                ->first();

            $previousHash = $previousTransaction
                ? $previousTransaction->hash
                : hash('sha256', "genesis_{$user->id}_{$currency->id}");

            // Create the transaction record
            $transaction = new Transaction([
                'user_id' => $user->id,
                'currency_id' => $currency->id,
                'amount' => $amount,
                'balance_before' => $balanceBefore,
                'balance_after' => $balanceAfter,
                'type' => $type,
                'reference_id' => $referenceId,
                'meta' => $meta,
                'previous_hash' => $previousHash,
                'hash' => 'temp-hash', // Temporary hash to satisfy NOT NULL constraint
                'created_at' => now(),
            ]);

            $transaction->save();

            // Generate and set the transaction hash
            $transaction->updateHash();
            $transaction->save();

            // Update the balance
            $balance->balance = $balanceAfter;
            $balance->updateHash($transaction->id, $transaction->hash);
            $balance->updated_at = now();
            $balance->save();

            // Verify integrity
            if (!$this->verifyTransactionIntegrity($transaction)) {
                throw new \RuntimeException('Transaction integrity verification failed');
            }

            Log::info('Balance transaction processed', [
                'user_id' => $user->id,
                'currency_id' => $currency->id,
                'amount' => $amount,
                'type' => $type,
                'transaction_id' => $transaction->id,
                'balance_before' => $balanceBefore,
                'balance_after' => $balanceAfter,
            ]);

            return $transaction;
        });
    }

    /**
     * Create initial balance record for a user and currency.
     */
    private function createInitialBalance(User $user, Currency $currency): UserBalance
    {
        $balance = new UserBalance([
            'user_id' => $user->id,
            'currency_id' => $currency->id,
            'balance' => 0,
            'updated_at' => now(),
        ]);

        $balance->updateHash();
        $balance->save();

        return $balance;
    }

    /**
     * Check if negative balance is allowed for a transaction type.
     */
    private function isNegativeBalanceAllowed(string $type): bool
    {
        $allowedTypes = ['adjustment', 'correction'];
        return in_array($type, $allowedTypes);
    }

    /**
     * Verify transaction integrity including hash chain.
     */
    public function verifyTransactionIntegrity(Transaction $transaction): bool
    {
        // Verify transaction hash
        if (!$transaction->verifyHash()) {
            Log::error('Transaction hash verification failed', ['transaction_id' => $transaction->id]);
            return false;
        }

        // Verify hash chain
        if (!$transaction->verifyChain()) {
            Log::error('Transaction chain verification failed', ['transaction_id' => $transaction->id]);
            return false;
        }

        // Verify balance hash
        $balance = UserBalance::where('user_id', $transaction->user_id)
            ->where('currency_id', $transaction->currency_id)
            ->first();

        if (!$balance) {
            Log::error('Balance not found', ['transaction_id' => $transaction->id]);
            return false;
        }

        // Get the latest transaction for this user and currency to verify balance hash
        $latestTransaction = Transaction::where('user_id', $transaction->user_id)
            ->where('currency_id', $transaction->currency_id)
            ->orderBy('id', 'desc')
            ->first();

        if (!$balance->verifyHash($latestTransaction?->id, $latestTransaction?->hash)) {
            Log::error('Balance hash verification failed', [
                'transaction_id' => $transaction->id,
                'latest_transaction_id' => $latestTransaction?->id,
                'balance_hash' => $balance->balance_hash,
                'expected_hash' => $balance->generateHash($latestTransaction?->id, $latestTransaction?->hash)
            ]);
            return false;
        }

        return true;
    }

    /**
     * Get user balance for a specific currency.
     */
    public function getBalance(User $user, Currency $currency): UserBalance
    {
        $balance = UserBalance::where('user_id', $user->id)
            ->where('currency_id', $currency->id)
            ->first();

        if (!$balance) {
            $balance = $this->createInitialBalance($user, $currency);
        }

        return $balance;
    }

    /**
     * Get all balances for a user.
     */
    public function getAllBalances(User $user): \Illuminate\Database\Eloquent\Collection
    {
        return UserBalance::where('user_id', $user->id)
            ->with('currency')
            ->get();
    }

    /**
     * Verify all transactions for a user and currency.
     */
    public function verifyUserTransactionChain(User $user, Currency $currency): bool
    {
        $transactions = Transaction::where('user_id', $user->id)
            ->where('currency_id', $currency->id)
            ->orderBy('id')
            ->get();

        foreach ($transactions as $transaction) {
            if (!$this->verifyTransactionIntegrity($transaction)) {
                return false;
            }
        }

        return true;
    }
}
