<?php

namespace App\Http\Controllers;

use App\Models\Currency;
use App\Models\Transaction;
use App\Services\BalanceService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class BalanceController extends Controller
{
    private BalanceService $balanceService;

    public function __construct(BalanceService $balanceService)
    {
        $this->balanceService = $balanceService;
    }

    /**
     * Get all balances for the authenticated user.
     */
    public function index(): JsonResponse
    {
        $user = Auth::user();
        $balances = $this->balanceService->getAllBalances($user);

        $formattedBalances = $balances->map(function ($balance) {
            return [
                'currency' => [
                    'id' => $balance->currency->id,
                    'code' => $balance->currency->code,
                    'symbol' => $balance->currency->symbol,
                    'decimals' => $balance->currency->decimals,
                ],
                'balance' => $balance->balance,
                'balance_formatted' => $balance->currency->fromMinorUnits($balance->balance),
                'updated_at' => $balance->updated_at,
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $formattedBalances,
        ]);
    }

    /**
     * Get balance for a specific currency.
     */
    public function show(Currency $currency): JsonResponse
    {
        $user = Auth::user();
        $balance = $this->balanceService->getBalance($user, $currency);

        return response()->json([
            'success' => true,
            'data' => [
                'currency' => [
                    'id' => $currency->id,
                    'code' => $currency->code,
                    'symbol' => $currency->symbol,
                    'decimals' => $currency->decimals,
                ],
                'balance' => $balance->balance,
                'balance_formatted' => $currency->fromMinorUnits($balance->balance),
                'updated_at' => $balance->updated_at,
            ],
        ]);
    }

    /**
     * Process a balance transaction.
     */
    public function processTransaction(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'currency_id' => 'required|exists:currencies,id',
            'amount' => 'required|numeric',
            'type' => 'required|string|max:50',
            'reference_id' => 'nullable|string|max:255',
            'meta' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $user = Auth::user();
        $currency = Currency::findOrFail($request->currency_id);

        // Convert amount to minor units
        $amountInMinorUnits = $currency->toMinorUnits($request->amount);

        try {
            $transaction = $this->balanceService->processTransaction(
                $user,
                $currency,
                $amountInMinorUnits,
                $request->type,
                $request->reference_id,
                $request->meta
            );

            return response()->json([
                'success' => true,
                'data' => [
                    'transaction_id' => $transaction->id,
                    'amount' => $transaction->amount,
                    'amount_formatted' => $currency->fromMinorUnits($transaction->amount),
                    'balance_before' => $transaction->balance_before,
                    'balance_before_formatted' => $currency->fromMinorUnits($transaction->balance_before),
                    'balance_after' => $transaction->balance_after,
                    'balance_after_formatted' => $currency->fromMinorUnits($transaction->balance_after),
                    'type' => $transaction->type,
                    'reference_id' => $transaction->reference_id,
                    'created_at' => $transaction->created_at,
                ],
            ], 201);
        } catch (\InvalidArgumentException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Transaction processing failed',
            ], 500);
        }
    }

    /**
     * Get transaction history for a specific currency.
     */
    public function transactions(Currency $currency, Request $request): JsonResponse
    {
        $user = Auth::user();

        $validator = Validator::make($request->all(), [
            'limit' => 'nullable|integer|min:1|max:100',
            'offset' => 'nullable|integer|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $limit = $request->get('limit', 20);
        $offset = $request->get('offset', 0);

        $transactions = Transaction::where('user_id', $user->id)
            ->where('currency_id', $currency->id)
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->offset($offset)
            ->get();

        $formattedTransactions = $transactions->map(function ($transaction) use ($currency) {
            return [
                'id' => $transaction->id,
                'amount' => $transaction->amount,
                'amount_formatted' => $currency->fromMinorUnits($transaction->amount),
                'balance_before' => $transaction->balance_before,
                'balance_before_formatted' => $currency->fromMinorUnits($transaction->balance_before),
                'balance_after' => $transaction->balance_after,
                'balance_after_formatted' => $currency->fromMinorUnits($transaction->balance_after),
                'type' => $transaction->type,
                'reference_id' => $transaction->reference_id,
                'meta' => $transaction->meta,
                'created_at' => $transaction->created_at,
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $formattedTransactions,
            'pagination' => [
                'limit' => $limit,
                'offset' => $offset,
                'total' => Transaction::where('user_id', $user->id)->where('currency_id', $currency->id)->count(),
            ],
        ]);
    }
}
